import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ComposedChart,
  Line,
  LineChart
} from 'recharts';
import { Box, Typography, Grid, Paper, Chip } from '@mui/material';

const COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f',
  purple: '#9c27b0',
  teal: '#00695c'
};

const BoqChart = ({ data }) => {
  if (!data) return null;

  // Prepara dati per grafici dalla nuova struttura distinta_materiali
  const caviData = data.distinta_materiali?.map((cavo, index) => ({
    ...cavo,
    // Mappa i nuovi campi ai vecchi per compatibilità
    metri_teorici: cavo.metri_teorici_totali,
    metri_reali: cavo.metri_reali_posati,
    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,
    color: Object.values(COLORS)[index % Object.values(COLORS).length],
    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),
    surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)
  })) || [];

  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)
  const bobineData = data.distinta_materiali?.filter(item => item.num_bobine > 0).map((bobina, index) => ({
    tipologia: bobina.tipologia,
    formazione: bobina.formazione,  // Aggiornato da sezione a formazione
    num_bobine: bobina.num_bobine,
    metri_disponibili: bobina.metri_disponibili,
    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,
    color: Object.values(COLORS)[index % Object.values(COLORS).length]
  })) || [];

  // Calcola totali per grafici a torta
  const totaliCavi = caviData.reduce((acc, cavo) => {
    acc.teorici += cavo.metri_teorici || 0;
    acc.reali += cavo.metri_reali || 0;
    acc.da_posare += cavo.metri_da_posare || 0;
    return acc;
  }, { teorici: 0, reali: 0, da_posare: 0 });

  const totaliData = [
    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },
    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },
    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }
  ];

  // Analisi deficit/surplus
  const analisiData = caviData.map(cavo => ({
    tipologia: cavo.tipologia_short,
    tipologia_full: cavo.tipologia,
    deficit: cavo.deficit,
    surplus: cavo.surplus,
    necessita_acquisto: cavo.deficit > 0
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      {/* Alert per Metri Orfani - Versione Migliorata */}
      {data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12}>
            <Paper sx={{ p: 0, border: '2px solid #d32f2f', borderRadius: 1, bgcolor: '#ffebee' }}>
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, color: '#d32f2f', mb: 2 }}>
                  🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA
                </Typography>

                <Typography variant="body1" sx={{ color: '#c62828', fontWeight: 500, mb: 2 }}>
                  <strong>{data.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA
                  ({data.metri_orfani.num_cavi_orfani} cavi)
                </Typography>

                {/* Dettaglio per tipologia se disponibile */}
                {data.metri_orfani.dettaglio_per_categoria && Object.keys(data.metri_orfani.dettaglio_per_categoria).length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ color: '#d32f2f', fontWeight: 600, mb: 1 }}>
                      Dettaglio per tipologia:
                    </Typography>
                    {Object.values(data.metri_orfani.dettaglio_per_categoria).map((categoria, index) => (
                      <Typography key={index} variant="body2" sx={{ color: '#8d6e63', ml: 2 }}>
                        • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi_orfani} cavi)
                      </Typography>
                    ))}
                  </Box>
                )}

                <Box sx={{ bgcolor: '#ffcdd2', p: 2, borderRadius: 1, border: '1px solid #ef5350' }}>
                  <Typography variant="body2" sx={{ color: '#b71c1c', fontWeight: 500, mb: 1 }}>
                    ⚠️ <strong>ATTENZIONE ACQUISTI:</strong>
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#8d6e63' }}>
                    Prima di acquistare nuovo materiale, associare questi metri a bobine esistenti o registrare nuove bobine.
                    <br />
                    <strong>Rischio:</strong> Sovra-acquisto di materiale se non gestito correttamente.
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Tabella Bill of Quantities Unificata */}
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Paper sx={{ p: 0, border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 2,
              borderBottom: '1px solid #e0e0e0'
            }}>
              <Typography variant="h6" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                📋 Bill of Quantities - Distinta Materiali
              </Typography>
              <Typography variant="body2" sx={{ color: '#666', mt: 0.5 }}>
                Riepilogo completo dei materiali per tipologia di cavo
              </Typography>
            </Box>

            <Box sx={{ overflow: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ backgroundColor: '#f8f9fa' }}>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'left',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Tipologia</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'center',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Formazione</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'center',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>N° Cavi</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Metri Teorici</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Metri Posati</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Metri Rimanenti</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Metri Disponibili</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Metri Mancanti</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'center',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0',
                      borderRight: '1px solid #f0f0f0'
                    }}>Necessita Acquisto</th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'center',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#2c3e50',
                      borderBottom: '1px solid #e0e0e0'
                    }}>N° Cavi Rimanenti</th>
                  </tr>
                </thead>
                <tbody>
                  {caviData.map((cavo, index) => {
                    const percentuale = cavo.metri_teorici > 0 ?
                      ((cavo.metri_reali || 0) / cavo.metri_teorici * 100) : 0;
                    const isCompleto = percentuale >= 100;
                    const isInCorso = percentuale > 0 && percentuale < 100;

                    return (
                      <tr key={index} style={{
                        backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'
                      }}>
                        <td style={{
                          padding: '12px 16px',
                          fontSize: '13px',
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0',
                          fontWeight: 500
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{
                              width: '4px',
                              height: '20px',
                              backgroundColor: cavo.color,
                              borderRadius: '2px'
                            }} />
                            {cavo.tipologia}
                          </Box>
                        </td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'center',
                          fontSize: '13px',
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{cavo.formazione || 'N/A'}</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'center',
                          fontSize: '13px',
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{cavo.num_cavi}</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{(cavo.metri_teorici || 0).toFixed(1)}m</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          color: COLORS.success,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{(cavo.metri_reali || 0).toFixed(1)}m</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          color: COLORS.warning,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{(cavo.metri_da_posare || 0).toFixed(1)}m</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          color: COLORS.info,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{(cavo.metri_disponibili || 0).toFixed(1)}m</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          color: cavo.metri_mancanti > 0 ? COLORS.error : COLORS.success,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{(cavo.metri_mancanti || 0).toFixed(1)}m</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'center',
                          fontSize: '13px',
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>
                          <Box sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            gap: 0.5,
                            px: 1,
                            py: 0.5,
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: 600,
                            backgroundColor: cavo.necessita_acquisto ? '#ffebee' : '#e8f5e8',
                            color: cavo.necessita_acquisto ? '#c62828' : '#2e7d32'
                          }}>
                            {cavo.necessita_acquisto ? '🛒 Sì' : '✅ No'}
                          </Box>
                        </td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'right',
                          fontSize: '13px',
                          fontWeight: 600,
                          color: isCompleto ? COLORS.success : isInCorso ? COLORS.warning : COLORS.secondary,
                          borderBottom: '1px solid #f0f0f0',
                          borderRight: '1px solid #f0f0f0'
                        }}>{percentuale.toFixed(1)}%</td>
                        <td style={{
                          padding: '12px 16px',
                          textAlign: 'center',
                          fontSize: '13px',
                          borderBottom: '1px solid #f0f0f0'
                        }}>
                          <Box sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            gap: 0.5,
                            px: 1,
                            py: 0.5,
                            borderRadius: '12px',
                            fontSize: '11px',
                            fontWeight: 600,
                            backgroundColor: isCompleto ? '#e8f5e8' : isInCorso ? '#fff3cd' : '#f8f9fa',
                            color: isCompleto ? '#2e7d32' : isInCorso ? '#856404' : '#6c757d'
                          }}>
                            {isCompleto ? '✅ Completato' : isInCorso ? '🔄 In Corso' : '⏳ Da Iniziare'}
                          </Box>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </Box>

            {/* Totali in fondo */}
            <Box sx={{
              bgcolor: '#f8f9fa',
              p: 2,
              borderTop: '1px solid #e0e0e0'
            }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                      {data.riepilogo?.totale_metri_teorici?.toFixed(1) || totaliCavi.teorici.toFixed(1)}m
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      Metri Teorici Totali
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ color: COLORS.success, fontWeight: 600 }}>
                      {data.riepilogo?.totale_metri_posati?.toFixed(1) || totaliCavi.reali.toFixed(1)}m
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      Metri Posati Totali
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ color: COLORS.warning, fontWeight: 600 }}>
                      {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || totaliCavi.da_posare.toFixed(1)}m
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      Metri Rimanenti
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ color: COLORS.primary, fontWeight: 600 }}>
                      {data.riepilogo?.percentuale_completamento?.toFixed(1) || (totaliCavi.teorici > 0 ? ((totaliCavi.reali / totaliCavi.teorici) * 100).toFixed(1) : 0)}%
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#666' }}>
                      Completamento Totale
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Spiegazione Logica BOQ Migliorata */}
            <Box sx={{
              bgcolor: '#e8f5e8',
              p: 3,
              borderTop: '1px solid #e0e0e0'
            }}>
              <Typography variant="body2" sx={{ color: '#2e7d32', fontWeight: 600, mb: 2 }}>
                📊 <strong>LOGICA CALCOLO BILL OF QUANTITIES</strong>
              </Typography>

              <Typography variant="body2" sx={{ color: '#388e3c', mb: 2 }}>
                <strong>Metri da acquistare per completamento progetto:</strong> {data.riepilogo?.totale_metri_mancanti?.toFixed(1) || 0}m
              </Typography>

              <Typography variant="body2" sx={{ color: '#4caf50', mb: 1 }}>
                <strong>Dettaglio calcolo:</strong>
              </Typography>
              <Typography variant="body2" sx={{ color: '#66bb6a', ml: 2 }}>
                • Metri ancora da posare: {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || 0}m
                <br />
                • Metri disponibili in magazzino: {data.riepilogo?.totale_metri_disponibili?.toFixed(1) || 0}m
                <br />
                • <strong>Fabbisogno netto:</strong> {data.riepilogo?.totale_metri_mancanti?.toFixed(1) || 0}m
              </Typography>

              {data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && (
                <Box sx={{ mt: 2, p: 2, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ffb74d' }}>
                  <Typography variant="body2" sx={{ color: '#f57c00', fontWeight: 500, mb: 1 }}>
                    ⚠️ <strong>NOTA IMPORTANTE:</strong>
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#ef6c00' }}>
                    I <strong>{data.metri_orfani.metri_orfani_totali}m posati con BOBINA_VUOTA</strong> NON sono inclusi nel calcolo acquisti.
                    <br />
                    Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BoqChart;
