"""
API endpoints semplificati per la gestione dei report del sistema CMS.
Versione semplificata che evita conflitti di import.
"""

from fastapi import APIRouter, HTTPException, status, Query
from typing import Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import sys
import os

# Aggiungi il percorso per importare il database
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

router = APIRouter()

@router.get("/{cantiere_id}/progress")
async def get_progress_report(
    cantiere_id: int,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$")
):
    """
    Genera un report dettagliato sull'avanzamento dei lavori.
    """
    try:
        from modules.database_pg import Database

        db = Database()

        # Verifica che il cantiere esista
        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            # Genera report per visualizzazione a schermo
            report_data = await generate_progress_data(cantiere_id, db)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            # Per PDF/Excel, restituisci un messaggio che la funzionalità sarà implementata
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del progress report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )

@router.get("/{cantiere_id}/boq")
async def get_bill_of_quantities(
    cantiere_id: int,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$")
):
    """
    Genera un report sulla distinta materiali (Bill of Quantities).
    """
    try:
        from modules.database_pg import Database

        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            try:
                report_data = await generate_boq_data(cantiere_id, db)
                return {
                    "success": True,
                    "content": report_data,
                    "formato": "video"
                }
            except Exception as report_error:
                # Cattura errori specifici della generazione del report
                logging.error(f"Errore nella generazione dati BOQ: {str(report_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Errore nel report Bill of Quantities: {str(report_error)}"
                )
        else:
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except HTTPException:
        # Rilancia le HTTPException già create
        raise
    except Exception as e:
        logging.error(f"Errore imprevisto nella generazione del Bill of Quantities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore imprevisto durante la generazione del report Bill of Quantities. Dettagli: {str(e)}"
        )

@router.get("/{cantiere_id}/bobine")
async def get_bobine_report(
    cantiere_id: int,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$")
):
    """
    Genera un report sull'utilizzo delle bobine.
    """
    try:
        from modules.database_pg import Database

        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            try:
                report_data = await generate_bobine_data(cantiere_id, db)
                return {
                    "success": True,
                    "content": report_data,
                    "formato": "video"
                }
            except Exception as report_error:
                # Cattura errori specifici della generazione del report
                logging.error(f"Errore nella generazione dati report bobine: {str(report_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Errore nel report Utilizzo Bobine: {str(report_error)}"
                )
        else:
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except HTTPException:
        # Rilancia le HTTPException già create
        raise
    except Exception as e:
        logging.error(f"Errore imprevisto nella generazione del report Utilizzo Bobine: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore imprevisto durante la generazione del report Utilizzo Bobine. Dettagli: {str(e)}"
        )

@router.get("/{cantiere_id}/bobina/{id_bobina}")
async def get_bobina_specifica_report(
    cantiere_id: int,
    id_bobina: str,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$")
):
    """
    Genera un report per una bobina specifica.
    """
    try:
        from modules.database_pg import Database

        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            report_data = await generate_bobina_specifica_data(cantiere_id, id_bobina, db)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del report bobina specifica: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )

@router.get("/{cantiere_id}/posa-periodo")
async def get_posa_periodo_report(
    cantiere_id: int,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$"),
    data_inizio: Optional[str] = Query(None, description="Data inizio periodo (YYYY-MM-DD)"),
    data_fine: Optional[str] = Query(None, description="Data fine periodo (YYYY-MM-DD)")
):
    """
    Genera un report sulla posa per periodo.
    """
    try:
        from modules.database_pg import Database

        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            report_data = await generate_posa_periodo_data(cantiere_id, data_inizio, data_fine, db)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del report posa periodo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )

@router.get("/{cantiere_id}/cavi-stato")
async def get_cavi_stato_report(
    cantiere_id: int,
    formato: str = Query(default="video", regex="^(pdf|excel|video)$")
):
    """
    Genera un report dei cavi suddivisi per stato.
    """
    try:
        from modules.database_pg import Database

        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            report_data = await generate_cavi_stato_data(cantiere_id, db)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            return {
                "success": True,
                "message": f"Generazione {formato.upper()} sarà implementata prossimamente",
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del report cavi per stato: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )

# Funzioni helper per la generazione dei dati dei report
async def generate_progress_data(cantiere_id: int, db) -> Dict[str, Any]:
    """Genera i dati per il report di avanzamento."""
    try:
        # Ottieni il nome del cantiere
        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Calcola i metri totali, posati e residui
        stats_result = db.execute_query("""
            SELECT
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_posati,
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE metratura_reale END) as metri_totali,
                COUNT(*) as totale_cavi,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN 1 ELSE 0 END) as cavi_posati
            FROM cavi
            WHERE id_cantiere = %s AND modificato_manualmente != 3
        """, (cantiere_id,), fetch_one=True)

        metri_totali = float(stats_result[2] or 0)
        metri_posati = float(stats_result[1] or 0)
        metri_da_posare = float(stats_result[0] or 0)
        totale_cavi = int(stats_result[3] or 0)
        cavi_posati = int(stats_result[4] or 0)

        # Calcola percentuale di avanzamento
        percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
        percentuale_cavi = (cavi_posati / totale_cavi * 100) if totale_cavi > 0 else 0

        # Calcola media giornaliera e dati di posa storica
        from datetime import datetime, timedelta

        # Ottieni TUTTI i giorni in cui è stata effettuata posa
        posa_storica = db.execute_query("""
            SELECT timestamp::date as data_posa, SUM(metratura_reale) as metri_posati
            FROM cavi
            WHERE id_cantiere = %s AND stato_installazione = 'Installato'
                AND timestamp IS NOT NULL AND metratura_reale > 0
            GROUP BY timestamp::date
            ORDER BY timestamp::date DESC
        """, (cantiere_id,), fetch_all=True)

        # Calcola media giornaliera realistica (solo giorni con posa effettiva)
        media_giornaliera = 0
        giorni_lavorativi_effettivi = 0
        totale_metri_posati_storico = 0

        if posa_storica:
            giorni_lavorativi_effettivi = len(posa_storica)
            totale_metri_posati_storico = sum(float(row[1]) for row in posa_storica)
            media_giornaliera = totale_metri_posati_storico / giorni_lavorativi_effettivi if giorni_lavorativi_effettivi > 0 else 0

        # Calcola giorni stimati per completamento
        giorni_stimati = None
        data_completamento = None

        if media_giornaliera > 0 and metri_da_posare > 0:
            giorni_stimati = int(metri_da_posare / media_giornaliera) + (1 if metri_da_posare % media_giornaliera > 0 else 0)

        # Prepara dati posa recente (ultimi 10 giorni per visualizzazione)
        posa_recente_display = posa_storica[:10] if posa_storica else []

        return {
            "nome_cantiere": nome_cantiere,
            "metri_totali": round(metri_totali, 2),
            "metri_posati": round(metri_posati, 2),
            "metri_da_posare": round(metri_da_posare, 2),
            "percentuale_avanzamento": round(percentuale_avanzamento, 2),
            "totale_cavi": totale_cavi,
            "cavi_posati": cavi_posati,
            "cavi_rimanenti": totale_cavi - cavi_posati,
            "percentuale_cavi": round(percentuale_cavi, 2),
            "media_giornaliera": round(media_giornaliera, 2),
            "giorni_stimati": giorni_stimati,
            "giorni_lavorativi_effettivi": giorni_lavorativi_effettivi,
            "posa_recente": [
                {
                    "data": row[0].strftime('%d/%m/%Y') if row[0] else '',
                    "metri": round(float(row[1]), 2)
                } for row in posa_recente_display
            ]
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati progress report: {str(e)}")
        raise

async def generate_boq_data(cantiere_id: int, db) -> Dict[str, Any]:
    """
    Genera i dati per il Bill of Quantities secondo le nuove specifiche.

    📊 Step principali:
    1. Estrazione dati cavi raggruppati per: tipologia, FORMAZIONE (sezione)
    2. Estrazione dati bobine filtrate per bobine non esaurite
    3. Match cavi ↔ bobine per categoria e calcolo metri mancanti

    Note:
    - Il campo 'sezione' nel database corrisponde a 'FORMAZIONE'
    - Il campo 'n_conduttori' è deprecato e non viene più utilizzato
    """
    try:
        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # 📊 Step 1: Estrazione dati cavi raggruppati per tipologia e FORMAZIONE (sezione)
        cavi_per_categoria = db.execute_query("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_cavi,
                COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
            FROM cavi
            WHERE id_cantiere = %s AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """, (cantiere_id,), fetch_all=True)

        # 📊 Step 2: Estrazione dati bobine filtrate per bobine non esaurite
        bobine_per_categoria = db.execute_query("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_bobine,
                SUM(metri_residui) as metri_disponibili
            FROM parco_cavi
            WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA' AND metri_residui > 0
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """, (cantiere_id,), fetch_all=True)

        # Converti bobine in dict per lookup rapido
        bobine_lookup = {}
        for row in bobine_per_categoria:
            key = f"{row[0]}_{row[1]}"  # tipologia_formazione
            bobine_lookup[key] = {
                "num_bobine": int(row[2]),
                "metri_disponibili": round(float(row[3] or 0), 2)
            }

        # 📊 Step 3: Match cavi ↔ bobine e calcolo metri mancanti (SENZA metri orfani)
        distinta_materiali = []
        for row in cavi_per_categoria:
            tipologia = row[0] or "N/A"
            formazione = row[1] or "N/A"  # Questo è il campo sezione rinominato come formazione
            num_cavi = int(row[2])
            num_cavi_rimanenti = int(row[3])  # Nuovo campo: numero di cavi ancora da installare
            metri_teorici_totali = round(float(row[4] or 0), 2)
            metri_reali_posati = round(float(row[5] or 0), 2)
            metri_da_posare = round(float(row[6] or 0), 2)

            # Lookup bobine per questa categoria usando tipologia + formazione
            key = f"{tipologia}_{formazione}"
            bobine_info = bobine_lookup.get(key, {"num_bobine": 0, "metri_disponibili": 0})

            # Verifica se questa tipologia ha metri orfani (BOBINA_VUOTA)
            metri_orfani_info = metri_orfani_per_categoria.get(key, {"metri_orfani": 0})
            ha_bobina_vuota = metri_orfani_info["metri_orfani"] > 0

            metri_disponibili = bobine_info["metri_disponibili"]
            # Calcolo SOLO per i metri ancora da posare (non include metri orfani)
            metri_mancanti_per_completamento = max(0, metri_da_posare - metri_disponibili)
            necessita_acquisto = metri_mancanti_per_completamento > 0

            distinta_materiali.append({
                "tipologia": tipologia,
                "formazione": formazione,  # Rinominato da sezione a formazione
                "num_cavi": num_cavi,
                "num_cavi_rimanenti": num_cavi_rimanenti,  # Nuovo campo
                "metri_teorici_totali": metri_teorici_totali,
                "metri_reali_posati": metri_reali_posati,
                "metri_da_posare": metri_da_posare,
                "num_bobine": bobine_info["num_bobine"],
                "metri_disponibili": metri_disponibili,
                "metri_mancanti": round(metri_mancanti_per_completamento, 2),
                "necessita_acquisto": necessita_acquisto,
                "ha_bobina_vuota": ha_bobina_vuota,  # Flag per evidenziare in rosso
                "metri_orfani": metri_orfani_info["metri_orfani"]  # Metri con BOBINA_VUOTA
            })

        # 📊 Step 4: Calcolo metri orfani (posati con BOBINA_VUOTA) per tipologia
        metri_orfani_per_tipologia = db.execute_query("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_cavi_orfani,
                SUM(metratura_reale) as metri_orfani_totali
            FROM cavi
            WHERE id_cantiere = %s
                AND id_bobina = 'BOBINA_VUOTA'
                AND stato_installazione = 'Installato'
                AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """, (cantiere_id,), fetch_all=True)

        # Converti in formato dict per tipologia
        metri_orfani_per_categoria = {}
        metri_orfani_totali_generali = 0
        num_cavi_orfani_totali = 0

        for row in metri_orfani_per_tipologia:
            tipologia = row[0] or "N/A"
            formazione = row[1] or "N/A"
            key = f"{tipologia}_{formazione}"
            metri_orfani = round(float(row[3] or 0), 2)

            metri_orfani_per_categoria[key] = {
                "tipologia": tipologia,
                "formazione": formazione,
                "num_cavi_orfani": int(row[2] or 0),
                "metri_orfani": metri_orfani
            }

            metri_orfani_totali_generali += metri_orfani
            num_cavi_orfani_totali += int(row[2] or 0)

        metri_orfani_info = {
            "num_cavi_orfani": num_cavi_orfani_totali,
            "metri_orfani_totali": round(metri_orfani_totali_generali, 2),
            "dettaglio_per_categoria": metri_orfani_per_categoria
        }

        # Calcola riepilogo generale
        totale_metri_teorici = sum(item["metri_teorici_totali"] for item in distinta_materiali)
        totale_metri_posati = sum(item["metri_reali_posati"] for item in distinta_materiali)
        totale_metri_da_posare = sum(item["metri_da_posare"] for item in distinta_materiali)
        totale_metri_disponibili = sum(item["metri_disponibili"] for item in distinta_materiali)
        totale_metri_mancanti = sum(item["metri_mancanti"] for item in distinta_materiali)
        categorie_necessitano_acquisto = sum(1 for item in distinta_materiali if item["necessita_acquisto"])

        return {
            "nome_cantiere": nome_cantiere,
            "distinta_materiali": distinta_materiali,
            "metri_orfani": metri_orfani_info,
            "riepilogo": {
                "totale_categorie": len(distinta_materiali),
                "totale_metri_teorici": round(totale_metri_teorici, 2),
                "totale_metri_posati": round(totale_metri_posati, 2),
                "totale_metri_da_posare": round(totale_metri_da_posare, 2),
                "totale_metri_disponibili": round(totale_metri_disponibili, 2),
                "totale_metri_mancanti": round(totale_metri_mancanti, 2),
                "categorie_necessitano_acquisto": categorie_necessitano_acquisto,
                "percentuale_completamento": round((totale_metri_posati / totale_metri_teorici * 100) if totale_metri_teorici > 0 else 0, 2)
            }
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati BOQ: {str(e)}")
        # Rilancia un'eccezione con un messaggio più specifico
        raise Exception(f"Impossibile generare il report Bill of Quantities: {str(e)}")

async def generate_bobine_data(cantiere_id: int, db) -> Dict[str, Any]:
    """Genera i dati per il report bobine."""
    try:
        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Query per ottenere tutte le bobine del cantiere
        # Aggiunta protezione contro divisione per zero e gestione valori NULL
        bobine = db.execute_query("""
            SELECT
                id_bobina,
                tipologia,
                sezione,
                metri_totali,
                metri_residui,
                stato_bobina,
                (COALESCE(metri_totali, 0) - COALESCE(metri_residui, 0)) as metri_utilizzati,
                CASE
                    WHEN COALESCE(metri_totali, 0) > 0 THEN ((COALESCE(metri_totali, 0) - COALESCE(metri_residui, 0)) / COALESCE(metri_totali, 1) * 100)
                    ELSE 0
                END as percentuale_utilizzo
            FROM parco_cavi
            WHERE id_cantiere = %s
            ORDER BY id_bobina
        """, (cantiere_id,), fetch_all=True)

        bobine_data = []
        for row in bobine:
            try:
                bobine_data.append({
                    "id_bobina": row[0] or "ID non specificato",
                    "tipologia": row[1] or "Non specificato",
                    "sezione": row[2] or "Non specificato",
                    "metri_totali": round(float(row[3] or 0), 2),
                    "metri_residui": round(float(row[4] or 0), 2),
                    "stato": row[5] or "Non specificato",
                    "metri_utilizzati": round(float(row[6] or 0), 2),
                    "percentuale_utilizzo": round(float(row[7] or 0), 2)
                })
            except (ValueError, TypeError) as conversion_error:
                logging.warning(f"Errore di conversione dati per bobina: {str(row)}: {str(conversion_error)}")
                # Continua con il prossimo record invece di fallire completamente

        # Anche se non ci sono bobine, restituisci un array vuoto invece di fallire
        return {
            "nome_cantiere": nome_cantiere,
            "bobine": bobine_data,
            "totale_bobine": len(bobine_data)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati report bobine: {str(e)}")
        # Rilancia un'eccezione con un messaggio più specifico
        raise Exception(f"Impossibile generare il report Utilizzo Bobine: {str(e)}")

async def generate_bobina_specifica_data(cantiere_id: int, id_bobina: str, db) -> Dict[str, Any]:
    """Genera i dati per il report bobina specifica."""
    try:
        # Costruisci l'ID completo della bobina se necessario
        if not id_bobina.startswith(f"C{cantiere_id}_B"):
            id_bobina_completo = f"C{cantiere_id}_B{id_bobina}"
        else:
            id_bobina_completo = id_bobina

        # Query per ottenere i dati della bobina
        bobina = db.execute_query("""
            SELECT
                id_bobina,
                tipologia,
                sezione,
                metri_totali,
                metri_residui,
                stato_bobina
            FROM parco_cavi
            WHERE id_cantiere = %s AND id_bobina = %s
        """, (cantiere_id, id_bobina_completo), fetch_one=True)

        if not bobina:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Bobina {id_bobina} non trovata"
            )

        # Query per ottenere i cavi associati alla bobina
        cavi_bobina = db.execute_query("""
            SELECT
                id_cavo,
                sistema,
                utility,
                tipologia,
                metri_teorici,
                metratura_reale,
                stato_installazione
            FROM cavi
            WHERE id_cantiere = %s AND id_bobina = %s
            ORDER BY id_cavo
        """, (cantiere_id, id_bobina_completo), fetch_all=True)

        cavi_data = []
        for row in cavi_bobina:
            cavi_data.append({
                "id_cavo": row[0],
                "sistema": row[1],
                "utility": row[2],
                "tipologia": row[3],
                "metri_teorici": round(float(row[4] or 0), 2),
                "metri_reali": round(float(row[5] or 0), 2),
                "stato": row[6]
            })

        metri_utilizzati = float(bobina[3] or 0) - float(bobina[4] or 0)
        percentuale_utilizzo = (metri_utilizzati / float(bobina[3] or 1)) * 100 if bobina[3] else 0

        return {
            "bobina": {
                "id_bobina": bobina[0],
                "tipologia": bobina[1],
                "sezione": bobina[2],
                "metri_totali": round(float(bobina[3] or 0), 2),
                "metri_residui": round(float(bobina[4] or 0), 2),
                "metri_utilizzati": round(metri_utilizzati, 2),
                "percentuale_utilizzo": round(percentuale_utilizzo, 2),
                "stato": bobina[5]
            },
            "cavi_associati": cavi_data,
            "totale_cavi": len(cavi_data)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati bobina specifica: {str(e)}")
        raise

async def generate_posa_periodo_data(cantiere_id: int, data_inizio: Optional[str], data_fine: Optional[str], db) -> Dict[str, Any]:
    """Genera i dati per il report posa per periodo."""
    try:
        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Se non specificate, usa gli ultimi 30 giorni
        if not data_fine:
            data_fine = datetime.now().date().isoformat()
        if not data_inizio:
            data_inizio = (datetime.now().date() - timedelta(days=30)).isoformat()

        # Query per posa giornaliera nel periodo
        posa_giornaliera = db.execute_query("""
            SELECT data_posa, SUM(metratura_reale) as metri_posati
            FROM cavi
            WHERE id_cantiere = %s
                AND data_posa BETWEEN %s AND %s
                AND stato_installazione = 'Installato'
            GROUP BY data_posa
            ORDER BY data_posa
        """, (cantiere_id, data_inizio, data_fine), fetch_all=True)

        # Calcola statistiche del periodo
        totale_metri_periodo = sum(float(row[1]) for row in posa_giornaliera)
        giorni_attivi = len(posa_giornaliera)
        media_giornaliera = totale_metri_periodo / giorni_attivi if giorni_attivi > 0 else 0

        posa_data = []
        for row in posa_giornaliera:
            posa_data.append({
                "data": row[0].strftime('%d/%m/%Y') if row[0] else '',
                "metri": round(float(row[1]), 2)
            })

        return {
            "nome_cantiere": nome_cantiere,
            "data_inizio": data_inizio,
            "data_fine": data_fine,
            "posa_giornaliera": posa_data,
            "totale_metri_periodo": round(totale_metri_periodo, 2),
            "giorni_attivi": giorni_attivi,
            "media_giornaliera": round(media_giornaliera, 2)
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati posa periodo: {str(e)}")
        raise

async def generate_cavi_stato_data(cantiere_id: int, db) -> Dict[str, Any]:
    """Genera i dati per il report cavi per stato."""
    try:
        cantiere_result = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,),
            fetch_one=True
        )
        nome_cantiere = cantiere_result[0] if cantiere_result else f"Cantiere {cantiere_id}"

        # Query per ottenere i cavi raggruppati per stato
        cavi_per_stato = db.execute_query("""
            SELECT
                stato_installazione,
                COUNT(*) as num_cavi,
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(metratura_reale) as metri_reali_totali
            FROM cavi
            WHERE id_cantiere = %s AND modificato_manualmente != 3
            GROUP BY stato_installazione
            ORDER BY stato_installazione
        """, (cantiere_id,), fetch_all=True)

        stati_data = []
        for row in cavi_per_stato:
            stati_data.append({
                "stato": row[0],
                "num_cavi": int(row[1]),
                "metri_teorici": round(float(row[2] or 0), 2),
                "metri_reali": round(float(row[3] or 0), 2)
            })

        return {
            "nome_cantiere": nome_cantiere,
            "cavi_per_stato": stati_data
        }
    except Exception as e:
        logging.error(f"Errore nella generazione dati cavi per stato: {str(e)}")
        raise
